---
# yaml-language-server: $schema=https://json.schemastore.org/helmfile

helmDefaults:
  cleanupOnFail: true
  wait: true
  waitForJobs: true

releases:
  - name: cilium
    namespace: kube-system
    chart: oci://ghcr.io/home-operations/charts-mirror/cilium
    version: 1.18.1
    values: ['{{ .RootDir }}/kubernetes/apps/kube-system/cilium/app/helm/values.yaml']

  - name: coredns
    namespace: kube-system
    chart: oci://ghcr.io/coredns/charts/coredns
    version: 1.43.2
    values: ['{{ .RootDir }}/kubernetes/apps/kube-system/coredns/app/helm/values.yaml']
    needs: ['kube-system/cilium']

  - name: spegel
    namespace: kube-system
    chart: oci://ghcr.io/spegel-org/helm-charts/spegel
    version: 0.3.0
    values: ['{{ .RootDir }}/kubernetes/apps/kube-system/spegel/app/helm/values.yaml']
    needs: ['kube-system/coredns']

  - name: cert-manager
    namespace: cert-manager
    chart: oci://quay.io/jetstack/charts/cert-manager
    version: v1.18.2
    values: ['{{ .RootDir }}/kubernetes/apps/cert-manager/cert-manager/app/helm/values.yaml']
    needs: ['kube-system/spegel']

  - name: external-secrets
    namespace: external-secrets
    chart: oci://ghcr.io/external-secrets/charts/external-secrets
    version: 0.19.2
    values: ['{{ .RootDir }}/kubernetes/apps/external-secrets/external-secrets/app/helm/values.yaml']
    needs: ['cert-manager/cert-manager']

  - name: flux-operator
    namespace: flux-system
    chart: oci://ghcr.io/controlplaneio-fluxcd/charts/flux-operator
    version: 0.28.0
    values: ['{{ .RootDir }}/kubernetes/apps/flux-system/flux-operator/app/helm/values.yaml']
    needs: ['external-secrets/external-secrets']

  - name: flux-instance
    namespace: flux-system
    chart: oci://ghcr.io/controlplaneio-fluxcd/charts/flux-instance
    version: 0.28.0
    values: ['{{ .RootDir }}/kubernetes/apps/flux-system/flux-instance/app/helm/values.yaml']
    needs: ['flux-system/flux-operator']
