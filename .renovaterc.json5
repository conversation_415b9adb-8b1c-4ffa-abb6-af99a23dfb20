{
  $schema: "https://docs.renovatebot.com/renovate-schema.json",
  extends: [
    "config:recommended",
    "docker:enableMajor",
    "helpers:pinGitHubActionDigests",
    "github>GizmoTickler/home-ops//.renovate/autoMerge.json5",
    "github>GizmoTickler/home-ops//.renovate/customManagers.json5",
    "github>GizmoTickler/home-ops//.renovate/grafanaDashboards.json5",
    "github>GizmoTickler/home-ops//.renovate/groups.json5",
    "github>GizmoTickler/home-ops//.renovate/labels.json5",
    "github>GizmoTickler/home-ops//.renovate/semanticCommits.json5",
    ":automergeBranch",
    ":disableRateLimiting",
    ":dependencyDashboard",
    ":semanticCommits",
    ":timezone(America/New_York)",
  ],
  dependencyDashboardTitle: "Renovate Dashboard 🤖",
  suppressNotifications: [
    "prEditedNotification",
    "prIgnoreNotification",
  ],
  ignorePaths: [
    "**/*.sops.*",
    "**/resources/**",
  ],
  flux: {
    managerFilePatterns: ["/(^|/)kubernetes/.+\\.ya?ml$/"]
  },
  "helm-values": {
    managerFilePatterns: ["/(^|/)kubernetes/.+\\.ya?ml$/"]
  },
  kubernetes: {
    managerFilePatterns: ["/(^|/)kubernetes/.+\\.ya?ml$/"]
  },
}
