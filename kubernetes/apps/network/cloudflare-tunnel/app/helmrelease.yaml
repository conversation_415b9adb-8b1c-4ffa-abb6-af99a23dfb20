---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/main/charts/other/app-template/schemas/helmrelease-helm-v2.schema.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: cloudflare-tunnel
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: app-template
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    controllers:
      cloudflare-tunnel:
        strategy: RollingUpdate
        annotations:
          reloader.stakater.com/auto: "true"
        containers:
          app:
            image:
              repository: docker.io/cloudflare/cloudflared
              tag: 2025.8.0@sha256:eb5c9324efe3b8e8a48612423dd912f76fa93cf138923e7eda5d1a9805057757
            env:
              NO_AUTOUPDATE: true
              TUNNEL_METRICS: 0.0.0.0:8080
              TUNNEL_ORIGIN_ENABLE_HTTP2: true
              TUNNEL_POST_QUANTUM: true
              TUNNEL_TRANSPORT_PROTOCOL: quic
            envFrom:
              - secretRef:
                  name: cloudflare-tunnel-secret
            args: ["tunnel", "run"]
            probes:
              liveness: &probes
                enabled: true
                custom: true
                spec:
                  httpGet:
                    path: /ready
                    port: &port 8080
                  initialDelaySeconds: 0
                  periodSeconds: 10
                  timeoutSeconds: 1
                  failureThreshold: 3
              readiness: *probes
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities: { drop: ["ALL"] }
            resources:
              requests:
                cpu: 10m
              limits:
                memory: 256Mi
    defaultPodOptions:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
    service:
      app:
        ports:
          http:
            port: *port
    serviceMonitor:
      app:
        endpoints:
          - port: http
    persistence:
      config-file:
        type: configMap
        name: cloudflare-tunnel-configmap
        globalMounts:
          - path: /etc/cloudflared/config.yaml
            subPath: config.yaml
            readOnly: true
