---
apiVersion: batch/v1
kind: Job
metadata:
  name: kopia-init
  annotations:
    argocd.argoproj.io/hook: PreSync
    argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
spec:
  ttlSecondsAfterFinished: 300
  template:
    metadata:
      name: kopia-init
    spec:
      restartPolicy: OnFailure
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        fsGroupChangePolicy: OnRootMismatch
      containers:
        - name: kopia-init
          image: ghcr.io/home-operations/kopia:0.21.1@sha256:f666b5f2c1ea4649cd2bd703507d4b81c2b515782e8476ba4a145b091a704a53
          command:
            - /bin/sh
            - -c
            - |
              set -e
              echo "Checking if repository exists..."
              if kopia repository status --config-file=/config/repository.config 2>/dev/null; then
                echo "Repository already exists, skipping initialization"
                exit 0
              fi
              
              echo "Initializing new Kopia repository..."
              kopia repository create filesystem \
                --path=/repository \
                --config-file=/config/repository.config \
                --override-hostname=volsync.volsync-system.svc.cluster.local \
                --override-username=volsync \
                --description="volsync" \
                --disable-actions
              
              echo "Repository initialized successfully"
              kopia repository status --config-file=/config/repository.config
          env:
            - name: KOPIA_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: kopia-secret
                  key: KOPIA_PASSWORD
            - name: TZ
              value: America/New_York
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop: ["ALL"]
          volumeMounts:
            - name: config-file
              mountPath: /config/repository.config
              subPath: repository.config
              readOnly: true
            - name: repository
              mountPath: /repository
            - name: tmpfs
              mountPath: /config/cache
              subPath: cache
            - name: tmpfs
              mountPath: /config/logs
              subPath: logs
      volumes:
        - name: config-file
          configMap:
            name: kopia-repository-configmap
        - name: repository
          nfs:
            server: 192.168.120.10
            path: /mnt/flashstor/VolsyncKopia
        - name: tmpfs
          emptyDir: {}
