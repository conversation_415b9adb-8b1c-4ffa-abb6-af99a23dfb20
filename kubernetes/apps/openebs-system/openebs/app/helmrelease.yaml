---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/source.toolkit.fluxcd.io/ocirepository_v1.json
apiVersion: source.toolkit.fluxcd.io/v1
kind: OCIRepository
metadata:
  name: openebs
spec:
  interval: 5m
  layerSelector:
    mediaType: application/vnd.cncf.helm.chart.content.v1.tar+gzip
    operation: copy
  ref:
    tag: 4.3.2
  url: oci://ghcr.io/home-operations/charts-mirror/openebs
---
# yaml-language-server: $schema=https://kubernetes-schema.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: openebs
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: openebs
  install:
    disableHooks: true
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    disableHooks: true
    remediation:
      retries: 3
  values:
    localpv-provisioner:
      localpv:
        image:
          registry: quay.io/
        basePath: &hostPath /var/mnt/extra/openebs/local
      hostpathClass:
        enabled: true
        name: openebs-hostpath
        isDefaultClass: false
        basePath: *hostPath
      helperPod:
        image:
          registry: quay.io/
    openebs-crds:
      csi:
        volumeSnapshots:
          enabled: false
          keep: false
    zfs-localpv:
      enabled: false
    lvm-localpv:
      enabled: false
    mayastor:
      enabled: false
    engines:
      local:
        lvm:
          enabled: false
        zfs:
          enabled: false
      replicated:
        mayastor:
          enabled: false
    loki:
      enabled: false
    alloy:
      enabled: false
    minio:
      enabled: false
